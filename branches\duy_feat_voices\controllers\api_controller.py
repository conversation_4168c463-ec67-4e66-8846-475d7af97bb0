"""
API Controller - X<PERSON> lý các endpoint API và tích hợp bên ngoài
Handle API endpoints and external integrations
"""

from flask import Blueprint, jsonify, request, send_from_directory  # Flask components
from pathlib import Path  # Thư viện xử lý đường dẫn
import os  # Th<PERSON> viện hệ điều hành

from ai_config import get_available_prompts  # L<PERSON>y danh sách prompt có sẵn
from models.session_model import SessionModel  # Model session


class APIController:
    """Controller cho các endpoint API - Controller for API endpoints"""

    def __init__(self):
        """Khởi tạo API Controller"""
        self.blueprint = Blueprint('api', __name__, url_prefix='/api')  # Tạo Blueprint với prefix /api
        self.setup_routes()  # Thiết lập routes
        self.session_model = SessionModel()  # Khởi tạo model session
        print("🔌 API Controller initialized")

    def setup_routes(self):
        """Thiết lập các routes API - Setup API routes"""
        # Route lấy danh sách prompt templates
        self.blueprint.add_url_rule('/prompts', 'get_prompts', self.get_prompts, methods=['GET'])
        # Route lấy thông tin session hiện tại
        self.blueprint.add_url_rule('/session', 'get_session', self.get_session, methods=['GET'])
        # Route cập nhật session
        self.blueprint.add_url_rule('/session', 'update_session', self.update_session, methods=['POST'])
        # Route kiểm tra sức khỏe hệ thống
        self.blueprint.add_url_rule('/health', 'health_check', self.health_check, methods=['GET'])
        # Route tải file
        self.blueprint.add_url_rule('/download/<path:filename>', 'download_file', self.download_file, methods=['GET'])
        # Route lấy trạng thái session - Get session status route
        self.blueprint.add_url_rule('/session_status', 'get_session_status', self.get_session_status, methods=['GET'])
        # Route lấy kết quả OCR - Get OCR results route
        self.blueprint.add_url_rule('/ocr_results', 'get_ocr_results', self.get_ocr_results, methods=['GET'])
        # Route lấy ảnh đã tạo - Get generated images route
        self.blueprint.add_url_rule('/generated_images', 'get_generated_images', self.get_generated_images, methods=['GET'])
        # NEW ROUTE: Combined capture and generate workflow
        self.blueprint.add_url_rule('/combined_capture_generate', 'combined_capture_generate', self.combined_capture_generate, methods=['POST'])

        # Route retry AI generation với dữ liệu hiện có
        self.blueprint.add_url_rule('/retry_ai_generation', 'retry_ai_generation', self.retry_ai_generation, methods=['POST'])

    def get_prompts(self):
        """Endpoint API để lấy các template prompt có sẵn - API endpoint to get available prompt templates"""
        try:
            prompts = get_available_prompts()  # Lấy danh sách prompt
            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'prompts': prompts,  # Danh sách prompt
                'count': len(prompts)  # Số lượng prompt
            })
        except Exception as e:
            print(f"❌ Error getting prompts: {e}")
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to get prompts: {str(e)}'  # Thông báo lỗi
            }), 500

    def get_session(self):
        """Endpoint API để lấy thông tin session hiện tại - API endpoint to get current session information"""
        try:
            status = self.session_model.get_session_status()  # Lấy trạng thái session
            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'session': status  # Dữ liệu session
            })
        except Exception as e:
            print(f"❌ Error getting session: {e}")  # Log lỗi
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to get session: {str(e)}'  # Thông báo lỗi
            }), 500  # HTTP 500 Internal Server Error

    def update_session(self):
        """Endpoint API để cập nhật dữ liệu session - API endpoint to update session data"""
        try:
            if not request.is_json:  # Nếu request không phải JSON
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'Request must be JSON'  # Thông báo yêu cầu JSON
                }), 400  # HTTP 400 Bad Request

            data = request.get_json()  # Lấy dữ liệu JSON từ request

            # Đảm bảo session tồn tại - Ensure session exists
            if not self.session_model.current_session:  # Nếu chưa có session
                session_id = self.session_model.create_session()  # Tạo session mới
                print(f"Created new session: {session_id}")  # Log session mới

            # Cập nhật session với dữ liệu được cung cấp - Update session with provided data
            success = self.session_model.update_session(**data)  # Gọi hàm update session

            if success:  # Nếu cập nhật thành công
                return jsonify({
                    'status': 'success',  # Trạng thái thành công
                    'message': 'Session updated successfully',  # Thông báo thành công
                    'session': self.session_model.get_session_status()  # Trạng thái session mới
                })
            else:  # Nếu cập nhật thất bại
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'Failed to update session'  # Thông báo thất bại
                }), 500  # HTTP 500 Internal Server Error

        except Exception as e:
            print(f"❌ Error updating session: {e}")  # Log lỗi
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Failed to update session: {str(e)}'  # Thông báo lỗi chi tiết
            }), 500  # HTTP 500 Internal Server Error

    def health_check(self):
        """Endpoint API để kiểm tra sức khỏe hệ thống - API endpoint for health check"""
        try:
            # Kiểm tra các thư mục thiết yếu có tồn tại không - Check if essential directories exist
            directories = ['static/img', 'sessions', 'prompts', 'outputs']  # Danh sách thư mục cần thiết
            missing_dirs = []  # Danh sách thư mục bị thiếu

            for directory in directories:  # Lặp qua từng thư mục
                if not Path(directory).exists():  # Nếu thư mục không tồn tại
                    missing_dirs.append(directory)  # Thêm vào danh sách thiếu

            # Kiểm tra file prompt có tồn tại không - Check if prompt files exist
            prompts_dir = Path('prompts')  # Đường dẫn thư mục prompts
            prompt_files = list(prompts_dir.glob('*.txt')) if prompts_dir.exists() else []  # Danh sách file prompt

            # Tạo đối tượng trạng thái sức khỏe - Create health status object
            health_status = {
                'status': 'healthy' if not missing_dirs else 'warning',  # Trạng thái: khỏe mạnh hoặc cảnh báo
                'timestamp': self._get_current_timestamp(),  # Thời gian kiểm tra
                'directories': {  # Thông tin thư mục
                    'missing': missing_dirs,  # Danh sách thư mục bị thiếu
                    'total_checked': len(directories)  # Tổng số thư mục đã kiểm tra
                },
                'prompts': {  # Thông tin prompt
                    'available': len(prompt_files),  # Số lượng file prompt có sẵn
                    'files': [f.name for f in prompt_files]  # Danh sách tên file prompt
                },
                'session': self.session_model.get_session_status()  # Trạng thái session hiện tại
            }

            # Xác định status code dựa trên kết quả kiểm tra - Determine status code based on check results
            status_code = 200 if not missing_dirs else 206  # 200 OK hoặc 206 Partial Content

            return jsonify({
                'status': 'success',  # Trạng thái thành công
                'health': health_status  # Dữ liệu sức khỏe hệ thống
            }), status_code  # Trả về với status code phù hợp

        except Exception as e:
            print(f"❌ Health check error: {e}")  # Log lỗi health check
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Health check failed: {str(e)}',  # Thông báo lỗi chi tiết
                'health': {  # Thông tin sức khỏe khi có lỗi
                    'status': 'unhealthy',  # Trạng thái không khỏe mạnh
                    'timestamp': self._get_current_timestamp()  # Thời gian xảy ra lỗi
                }
            }), 500  # HTTP 500 Internal Server Error

    def download_file(self, filename):
        """Endpoint API để tải file - API endpoint to download files"""
        try:
            # Kiểm tra bảo mật - chỉ cho phép tải từ thư mục cụ thể - Security check - only allow downloads from specific directories
            allowed_dirs = ['outputs', 'static/img']  # Danh sách thư mục được phép
            file_path = None  # Đường dẫn file được tìm thấy

            # Tìm file trong các thư mục được phép - Search for file in allowed directories
            for directory in allowed_dirs:  # Lặp qua từng thư mục được phép
                potential_path = Path(directory) / filename  # Tạo đường dẫn tiềm năng
                if potential_path.exists() and potential_path.is_file():  # Nếu file tồn tại và là file
                    file_path = potential_path  # Lưu đường dẫn file
                    break  # Thoát khỏi vòng lặp

            if not file_path:  # Nếu không tìm thấy file
                return jsonify({
                    'status': 'error',  # Trạng thái lỗi
                    'message': 'File not found'  # Thông báo không tìm thấy file
                }), 404  # HTTP 404 Not Found

            # Gửi file - Send file
            return send_from_directory(
                file_path.parent,  # Thư mục chứa file
                file_path.name,  # Tên file
                as_attachment=True,  # Gửi dưới dạng attachment
                download_name=filename  # Tên file khi tải về
            )

        except Exception as e:
            print(f"❌ Download error: {e}")  # Log lỗi tải file
            return jsonify({
                'status': 'error',  # Trạng thái lỗi
                'message': f'Download failed: {str(e)}'  # Thông báo lỗi chi tiết
            }), 500  # HTTP 500 Internal Server Error

    def _get_current_timestamp(self):
        """Lấy timestamp hiện tại theo định dạng ISO - Get current timestamp in ISO format"""
        from datetime import datetime  # Import datetime để lấy thời gian
        return datetime.now().isoformat()  # Trả về thời gian hiện tại theo định dạng ISO

    def get_session_status(self):
        """
        Lấy trạng thái session hiện tại - Get current session status

        API endpoint để frontend lấy thông tin session hiện tại
        bao gồm card_info và generated_images
        """
        try:
            from controllers.camera_controller import camera_controller

            # Lấy session hiện tại - Get current session
            session = camera_controller.session_model.current_session

            if session:
                return jsonify({
                    'status': 'success',
                    'session': {
                        'session_id': session.get('session_id'),
                        'card_info': session.get('card_info', {}),
                        'generated_images': session.get('generated_images', []),
                        'status': session.get('status', 'unknown')
                    }
                })
            else:
                return jsonify({
                    'status': 'success',
                    'session': None,
                    'message': 'No active session'
                })

        except Exception as e:
            print(f"❌ Error getting session status: {e}")
            return jsonify({
                'status': 'error',
                'error': str(e)
            }), 500

    def get_ocr_results(self):
        """
        API endpoint để lấy kết quả OCR từ session hiện tại
        Get OCR results from current session
        """
        try:
            from controllers.camera_controller import camera_controller

            # Lấy session hiện tại - Get current session
            session = camera_controller.session_model.current_session

            # Debug session state
            print(f"🔍 OCR Results API Debug:")
            print(f"   Session exists: {session is not None}")
            if session:
                print(f"   Session ID: {session.get('session_id', 'N/A')}")
                print(f"   Has card_info: {'card_info' in session}")
                print(f"   Card info keys: {list(session.get('card_info', {}).keys()) if session.get('card_info') else 'None'}")

            if session and session.get('card_info'):
                card_info = session.get('card_info', {})

                # Kiểm tra chất lượng OCR data
                non_empty_fields = sum(1 for v in card_info.values() if v and str(v).strip())
                total_fields = len(card_info)

                print(f"✅ OCR Results API: Returning {non_empty_fields}/{total_fields} fields")

                # Tìm audio file từ session và convert to web URL
                audio_file = None
                if session.get('tts_audio_path'):
                    audio_path = session.get('tts_audio_path')
                    # Convert local path to web URL
                    if audio_path.startswith('outputs/'):
                        audio_file = f"/{audio_path}"
                    elif 'outputs' in audio_path:
                        # Extract path after outputs
                        path_part = audio_path.split('outputs')[-1].lstrip('\\/')
                        audio_file = f"/outputs/{path_part}"

                return jsonify({
                    'status': 'success',
                    'ocr_data': card_info,
                    'session_id': session.get('session_id'),
                    'timestamp': session.get('updated_at', session.get('created_at')),
                    'audio_file': audio_file,
                    'quality': {
                        'fields_extracted': non_empty_fields,
                        'total_fields': total_fields,
                        'percentage': round((non_empty_fields / total_fields) * 100, 1) if total_fields > 0 else 0
                    }
                })
            else:
                print(f"❌ OCR Results API: No data available")
                print(f"   Session: {session is not None}")
                print(f"   Card info: {session.get('card_info') if session else 'No session'}")

                return jsonify({
                    'status': 'error',
                    'message': 'No OCR data available. Please capture and process business card first.',
                    'ocr_data': {},
                    'debug': {
                        'session_exists': session is not None,
                        'session_id': session.get('session_id') if session else None,
                        'has_card_info': bool(session and session.get('card_info')) if session else False
                    }
                }), 404

        except Exception as e:
            print(f"❌ Error getting OCR results: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Failed to get OCR results: {str(e)}',
                'ocr_data': {}
            }), 500

    def get_generated_images(self):
        """
        API endpoint để lấy danh sách ảnh đã tạo từ session hiện tại
        Get generated images list from current session
        """
        try:
            from controllers.camera_controller import camera_controller

            # Lấy session hiện tại - Get current session
            session = camera_controller.session_model.current_session

            if session and session.get('generated_images'):
                # Xử lý đường dẫn ảnh để có thể truy cập từ web
                images = []
                for img_info in session.get('generated_images', []):
                    if isinstance(img_info, dict):
                        # Định dạng đầy đủ với metadata
                        image_data = {
                            'url': self._convert_to_web_path(img_info.get('path')),
                            'title': img_info.get('title', 'Generated Image'),
                            'filename': img_info.get('filename'),
                            'created_at': img_info.get('created_at'),
                            'image_index': img_info.get('image_index', len(images) + 1)
                        }
                    else:
                        # Định dạng đơn giản chỉ có đường dẫn
                        image_data = {
                            'url': self._convert_to_web_path(str(img_info)),
                            'title': f'Generated Image {len(images) + 1}',
                            'filename': Path(str(img_info)).name,
                            'image_index': len(images) + 1
                        }
                    images.append(image_data)

                return jsonify({
                    'status': 'success',
                    'images': images,
                    'count': len(images),
                    'session_id': session.get('session_id'),
                    'timestamp': session.get('updated_at', session.get('created_at'))
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'No generated images available. Please process images first.',
                    'images': [],
                    'count': 0
                }), 404

        except Exception as e:
            print(f"❌ Error getting generated images: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Failed to get generated images: {str(e)}',
                'images': [],
                'count': 0
            }), 500

    def _convert_to_web_path(self, file_path):
        """
        Chuyển đổi đường dẫn file thành đường dẫn web có thể truy cập
        Convert file path to accessible web path
        """
        if not file_path:
            return None

        # Chuyển đổi thành đối tượng Path
        path = Path(file_path)

        # Nếu đã là đường dẫn web, trả về như vậy
        if str(path).startswith(('http://', 'https://', '/')):
            return str(path)

        # Nếu trong thư mục outputs, tạo đường dẫn web
        if 'outputs' in str(path):
            # Tìm phần sau 'outputs' để tạo đường dẫn web
            parts = path.parts
            try:
                outputs_index = parts.index('outputs')
                web_path = '/outputs/' + '/'.join(parts[outputs_index + 1:])
                return web_path.replace('\\', '/')
            except ValueError:
                pass

        # Nếu trong thư mục static, tạo đường dẫn web
        if 'static' in str(path):
            parts = path.parts
            try:
                static_index = parts.index('static')
                web_path = '/static/' + '/'.join(parts[static_index + 1:])
                return web_path.replace('\\', '/')
            except ValueError:
                pass

        # Mặc định: trả về đường dẫn tương đối
        return str(path).replace('\\', '/')

    def combined_capture_generate(self):
        """
        NEW ENDPOINT: Combined capture and AI generation workflow
        Handles: Capture both cameras → OCR with Gemini 2.5 → AI Generation with Gemini 2.0 Flash
        """
        try:
            print("🚀 Starting Combined Capture & Generate Workflow")

            # Import required controllers
            from controllers.camera_controller import camera_controller
            from controllers.processing_controller import processing_controller

            # Parse request data
            data = {}
            if request.is_json and request.get_json():
                data = request.get_json()

            prompt_template = data.get('prompt_template', 'cartoon')
            print(f"🎨 Selected style: {prompt_template}")

            # STEP 1: Ensure session exists
            if not camera_controller.session_model.current_session:
                session_id = camera_controller.session_model.create_session()
                print(f"🆕 Created new session: {session_id}")

            session = camera_controller.session_model.current_session
            print(f"📁 Using session: {session.get('session_id')}")

            # STEP 2: Capture both images if not already captured
            capture_results = {}

            # Capture business card (camera 0) if not captured
            if not session.get('card_captured'):
                print("📸 Capturing business card...")
                card_result = camera_controller.capture_step(0)
                if hasattr(card_result, 'get_json'):
                    card_data = card_result.get_json()
                    if card_data.get('status') != 'success':
                        return jsonify({
                            'status': 'error',
                            'message': f'Business card capture failed: {card_data.get("message")}'
                        }), 500
                    capture_results['card'] = card_data
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'Business card capture failed'
                    }), 500

            # Capture face (camera 1) if not captured
            if not session.get('face_captured'):
                print("📸 Capturing face...")
                face_result = camera_controller.capture_step(1)
                if hasattr(face_result, 'get_json'):
                    face_data = face_result.get_json()
                    if face_data.get('status') != 'success':
                        return jsonify({
                            'status': 'error',
                            'message': f'Face capture failed: {face_data.get("message")}'
                        }), 500
                    capture_results['face'] = face_data
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'Face capture failed'
                    }), 500

            # STEP 3: Process images with AI Pipeline
            print("🤖 Starting AI processing...")

            # Create processing request
            processing_request = type('MockRequest', (), {
                'is_json': True,
                'get_json': lambda: {'prompt_template': prompt_template}
            })()

            # Temporarily replace Flask request with our mock
            import flask
            original_request = flask.request
            flask.request = processing_request

            try:
                # Call processing controller
                processing_result = processing_controller.process_images()

                if hasattr(processing_result, 'get_json'):
                    result_data = processing_result.get_json()

                    if result_data.get('status') == 'success':
                        print("✅ Combined workflow completed successfully!")

                        # Resume camera streams after successful processing
                        camera_controller.streaming_active_0 = True
                        camera_controller.streaming_active_1 = True
                        camera_controller.capture_mode_0 = False
                        camera_controller.capture_mode_1 = False

                        return jsonify({
                            'status': 'success',
                            'message': 'Combined capture and generation completed successfully',
                            'capture_results': capture_results,
                            'card_info': result_data.get('card_info', {}),
                            'generated_images': result_data.get('generated_images', []),
                            'session_id': session.get('session_id'),
                            'workflow_completed': True
                        })
                    else:
                        return jsonify({
                            'status': 'error',
                            'message': f'AI processing failed: {result_data.get("message")}',
                            'capture_results': capture_results
                        }), 500
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'AI processing returned invalid response',
                        'capture_results': capture_results
                    }), 500

            finally:
                # Restore original request
                flask.request = original_request

        except Exception as e:
            print(f"❌ Combined workflow error: {e}")
            import traceback
            traceback.print_exc()

            # Resume camera streams on error
            try:
                from controllers.camera_controller import camera_controller
                camera_controller.streaming_active_0 = True
                camera_controller.streaming_active_1 = True
                camera_controller.capture_mode_0 = False
                camera_controller.capture_mode_1 = False
            except:
                pass

            return jsonify({
                'status': 'error',
                'message': f'Combined workflow failed: {str(e)}',
                'details': 'Check server logs for full error details'
            }), 500

    def retry_ai_generation(self):
        """
        API endpoint để tạo ảnh AI mới với dữ liệu OCR hiện có
        Retry AI generation with existing OCR data
        """
        try:
            from controllers.camera_controller import camera_controller

            # Lấy session hiện tại
            session = camera_controller.session_model.current_session
            if not session:
                return jsonify({
                    'status': 'error',
                    'message': 'Không có session hiện tại. Vui lòng chụp ảnh trước.'
                }), 400

            # Kiểm tra có dữ liệu OCR không
            card_info = session.get('card_info')
            if not card_info:
                return jsonify({
                    'status': 'error',
                    'message': 'Không có dữ liệu OCR. Vui lòng chụp và xử lý business card trước.'
                }), 400

            # Kiểm tra có face image không
            face_image_path = session.get('face_image_path')
            if not face_image_path:
                return jsonify({
                    'status': 'error',
                    'message': 'Không có ảnh khuôn mặt. Vui lòng chụp ảnh face trước.'
                }), 400

            # Lấy prompt template từ request hoặc sử dụng mặc định
            request_data = request.get_json() if request.is_json else {}
            prompt_template = request_data.get('prompt_template', 'cartoon')  # Mặc định cartoon

            print(f"🎨 Retry AI Generation API called")
            print(f"   Session ID: {session.get('session_id')}")
            print(f"   Face Image: {face_image_path}")
            print(f"   Template: {prompt_template}")
            print(f"   Person: {card_info.get('name', 'Unknown')}")

            # Gọi processing controller để tạo ảnh AI
            from controllers.processing_controller import processing_controller

            print(f"🎨 Calling processing_controller.retry_ai_generation()...")

            # Sử dụng method retry_ai_generation hiện có - method này trả về Flask response
            return processing_controller.retry_ai_generation()

        except Exception as e:
            print(f"❌ Error in retry_ai_generation: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Lỗi server khi tạo ảnh AI: {str(e)}'
            }), 500


class MainController:
    """Controller chính cho các route cơ bản - Main controller for basic routes"""

    def __init__(self):
        """Khởi tạo Main Controller"""
        self.blueprint = Blueprint('main', __name__)  # Tạo Blueprint cho main routes
        self.setup_routes()  # Thiết lập các routes
        self.session_model = SessionModel()  # Khởi tạo model session
        print("🏠 Main Controller initialized")  # Log khởi tạo thành công

    def setup_routes(self):
        """Thiết lập các routes chính - Setup main routes"""
        # Route trang chủ
        self.blueprint.add_url_rule('/', 'index', self.index)
        # Route phục vụ file static
        self.blueprint.add_url_rule('/static/<path:filename>', 'static_files', self.static_files)
        # Route phục vụ file output
        self.blueprint.add_url_rule('/outputs/<path:filename>', 'output_files', self.output_files)

    def index(self):
        """Trang chủ chính - Main index page"""
        try:
            from flask import render_template  # Import để render template

            # Đảm bảo session tồn tại - Ensure session exists
            if not self.session_model.current_session:  # Nếu chưa có session
                session_id = self.session_model.create_session()  # Tạo session mới
                print(f"Created new session for index: {session_id}")  # Log session mới

            return render_template('combined_file.html')  # Render trang combined_file.html

        except Exception as e:
            print(f"❌ Index error: {e}")  # Log lỗi trang chủ
            return f"Error loading page: {str(e)}", 500  # Trả về lỗi 500

    def _get_relative_path(self, file_path):
        """Chuyển đổi đường dẫn tuyệt đối thành đường dẫn tương đối để phục vụ web - Convert absolute path to relative path for web serving"""
        if not file_path:  # Nếu không có đường dẫn
            return None  # Trả về None

        # Chuyển đổi thành đối tượng Path - Convert to Path object
        path = Path(file_path)

        # Nếu đã là đường dẫn tương đối đến static, trả về như vậy - If it's already relative to static, return as is
        if 'static' in str(path):  # Nếu chứa 'static'
            return str(path).replace('\\', '/')  # Thay thế backslash bằng forward slash

        # Nếu trong thư mục outputs, trả về đường dẫn tương đối - If it's in outputs folder, return relative path
        if 'outputs' in str(path):  # Nếu chứa 'outputs'
            return str(path).replace('\\', '/')  # Thay thế backslash bằng forward slash

        # Mặc định: chỉ trả về tên file - Default: return filename only
        return path.name  # Trả về tên file

    def static_files(self, filename):
        """Phục vụ file static - Serve static files"""
        try:
            from flask import send_from_directory  # Import để phục vụ file
            return send_from_directory('static', filename)  # Phục vụ file từ thư mục static
        except Exception as e:
            print(f"❌ Static file error: {e}")  # Log lỗi file static
            return "File not found", 404  # Trả về lỗi 404

    def output_files(self, filename):
        """Phục vụ file output - Serve output files"""
        try:
            from flask import send_from_directory  # Import để phục vụ file
            print(f"📁 Serving output file: {filename}")  # Log file đang phục vụ
            return send_from_directory('outputs', filename)  # Phục vụ file từ thư mục outputs
        except Exception as e:
            print(f"❌ Output file error: {e}")  # Log lỗi file output
            return "File not found", 404  # Trả về lỗi 404


api_controller = APIController()  # Instance controller API
api_controller = APIController()  # Instance controller API
main_controller = MainController()  # Instance controller chính

def get_api_blueprint():
    """Lấy blueprint của API controller - Get API controller blueprint"""
    return api_controller.blueprint  # Trả về blueprint API

def get_main_blueprint():
    """Lấy blueprint của main controller - Get main controller blueprint"""
    return main_controller.blueprint  # Trả về blueprint chính
