/**
 * Apple-style Interactions for AI Generator
 * Smooth animations and micro-interactions inspired by Apple's design
 */

// Apple-style smooth scrolling
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Initialize Apple-style animations
    initAppleAnimations();
    
    // Initialize navigation interactions
    initNavigationInteractions();
    
    // Initialize button interactions
    initButtonInteractions();
    
    // Initialize card interactions
    initCardInteractions();
});

/**
 * Initialize Apple-style entrance animations
 */
function initAppleAnimations() {
    // Fade in elements on page load
    const animatedElements = document.querySelectorAll('.apple-card, .apple-camera-card, .apple-button');
    
    animatedElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * Initialize navigation interactions
 */
function initNavigationInteractions() {
    const nav = document.querySelector('.apple-nav');
    if (!nav) return;
    
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            nav.style.background = 'rgba(255, 255, 255, 0.95)';
            nav.style.backdropFilter = 'blur(20px)';
        } else {
            nav.style.background = 'rgba(255, 255, 255, 0.8)';
            nav.style.backdropFilter = 'blur(20px)';
        }
        
        lastScrollY = currentScrollY;
    });
}

/**
 * Initialize button interactions with Apple-style feedback
 */
function initButtonInteractions() {
    const buttons = document.querySelectorAll('.apple-button');
    
    buttons.forEach(button => {
        // Add ripple effect on click
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('apple-ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // Add hover effects
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.2)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
    });
}

/**
 * Initialize card interactions
 */
function initCardInteractions() {
    const cards = document.querySelectorAll('.apple-card, .apple-camera-card, .apple-image-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
    });
}

/**
 * Apple-style loading animation
 */
function showAppleLoading(element) {
    element.classList.add('apple-loading');
    element.style.pointerEvents = 'none';
    
    const spinner = document.createElement('div');
    spinner.className = 'apple-spinner';
    spinner.innerHTML = `
        <div class="apple-spinner-dot"></div>
        <div class="apple-spinner-dot"></div>
        <div class="apple-spinner-dot"></div>
    `;
    
    element.appendChild(spinner);
}

function hideAppleLoading(element) {
    element.classList.remove('apple-loading');
    element.style.pointerEvents = 'auto';
    
    const spinner = element.querySelector('.apple-spinner');
    if (spinner) {
        spinner.remove();
    }
}

/**
 * Apple-style notification system
 */
function showAppleNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `apple-notification apple-notification-${type}`;
    notification.innerHTML = `
        <div class="apple-notification-content">
            <span class="apple-notification-icon">${getNotificationIcon(type)}</span>
            <span class="apple-notification-message">${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.add('apple-notification-show');
    }, 100);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('apple-notification-show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };
    return icons[type] || icons.info;
}

/**
 * Apple-style progress animation
 */
function updateAppleProgress(percentage) {
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        progressFill.style.width = percentage + '%';
        
        // Add shimmer effect during progress
        if (percentage > 0 && percentage < 100) {
            progressFill.style.backgroundImage = 'linear-gradient(90deg, var(--color-primary), var(--apple-blue-dark), var(--color-primary))';
            progressFill.style.backgroundSize = '200% 100%';
            progressFill.style.animation = 'shimmer 2s infinite';
        } else {
            progressFill.style.animation = 'none';
        }
    }
}

/**
 * Apple-style image preview with zoom
 */
function initImagePreview() {
    const images = document.querySelectorAll('.apple-camera-feed, .apple-image-display img');
    
    images.forEach(img => {
        img.addEventListener('click', function() {
            showImagePreview(this.src);
        });
    });
}

function showImagePreview(src) {
    const overlay = document.createElement('div');
    overlay.className = 'apple-image-preview-overlay';
    overlay.innerHTML = `
        <div class="apple-image-preview-container">
            <img src="${src}" alt="Preview" class="apple-image-preview">
            <button class="apple-image-preview-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Animate in
    setTimeout(() => {
        overlay.classList.add('apple-image-preview-show');
    }, 50);
    
    // Close on click
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay || e.target.classList.contains('apple-image-preview-close')) {
            overlay.classList.remove('apple-image-preview-show');
            setTimeout(() => {
                overlay.remove();
            }, 300);
        }
    });
}

// Export functions for global use
window.AppleUI = {
    showLoading: showAppleLoading,
    hideLoading: hideAppleLoading,
    showNotification: showAppleNotification,
    updateProgress: updateAppleProgress,
    initImagePreview: initImagePreview
};
