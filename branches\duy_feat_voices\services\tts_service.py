"""
TTS Service - Chuyển đổi văn bản thành giọng nói
"""

import os
import re
from pathlib import Path
from typing import Optional, Dict
import time
import uuid

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    print("⚠️ gTTS chưa được cài đặt. Chạy: pip install gtts")

import logging
logger = logging.getLogger(__name__)

class TTSService:
    """Dịch vụ chuyển đổi văn bản thành giọng nói"""

    def __init__(self, audio_dir: str = "static/audio", templates_dir: str = "tts_templates"):
        """Khởi tạo dịch vụ TTS"""
        self.audio_dir = Path(audio_dir)
        self.audio_dir.mkdir(parents=True, exist_ok=True)

        # Thư mục chứa template TTS
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)

        # Các ngôn ngữ được hỗ trợ
        self.supported_languages = {
            'vi': 'Tiếng Việt',
            'en': 'Tiếng Anh',
            'ja': 'Tiếng Nhật'
        }

        # Mẫu nhận diện ngôn ngữ
        self.language_patterns = {
            'vi': r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]',
            'ja': r'[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]'
        }

        # Bộ nhớ đệm cho file audio
        self.audio_cache: Dict[str, str] = {}

        if not GTTS_AVAILABLE:
            logger.warning("⚠️ gTTS chưa có sẵn - Tính năng TTS bị vô hiệu hóa")

    def detect_language(self, text: str) -> str:
        """Nhận diện ngôn ngữ của văn bản"""
        if not text or not text.strip():
            return 'en'

        text = text.strip()

        # Kiểm tra tiếng Việt
        if re.search(self.language_patterns['vi'], text):
            return 'vi'

        # Kiểm tra tiếng Nhật
        if re.search(self.language_patterns['ja'], text):
            return 'ja'

        # Mặc định là tiếng Anh
        return 'en'

    def generate_audio_filename(self, text: str, lang: str) -> str:
        """Tạo tên file audio duy nhất"""
        text_hash = str(hash(f"{text}_{lang}"))[-8:]
        timestamp = str(int(time.time()))[-6:]
        unique_id = str(uuid.uuid4())[:8]
        return f"tts_{lang}_{text_hash}_{timestamp}_{unique_id}.mp3"

    def text_to_speech(self, text: str, lang: Optional[str] = None, slow: bool = False) -> Optional[str]:
        """Chuyển văn bản thành giọng nói và lưu file audio"""
        if not GTTS_AVAILABLE:
            return None

        if not text or not text.strip():
            return None

        text = text.strip()

        # Tự động nhận diện ngôn ngữ nếu không được cung cấp
        if not lang:
            lang = self.detect_language(text)

        # Kiểm tra ngôn ngữ có được hỗ trợ không
        if lang not in self.supported_languages:
            lang = 'en'

        # Kiểm tra bộ nhớ đệm
        cache_key = f"{text}_{lang}_{slow}"
        if cache_key in self.audio_cache:
            audio_path = self.audio_cache[cache_key]
            if os.path.exists(audio_path):
                return audio_path
            else:
                del self.audio_cache[cache_key]

        try:
            # Tạo tên file audio
            filename = self.generate_audio_filename(text, lang)
            audio_path = self.audio_dir / filename

            # Tạo đối tượng gTTS
            tts = gTTS(text=text, lang=lang, slow=slow)

            # Lưu file audio
            tts.save(str(audio_path))

            # Thêm vào bộ nhớ đệm
            self.audio_cache[cache_key] = str(audio_path)

            return str(audio_path)

        except Exception:
            return None

    def get_supported_languages(self) -> Dict[str, str]:
        """Lấy danh sách ngôn ngữ được hỗ trợ"""
        return self.supported_languages.copy()

    def load_template(self, template_name: str, lang: str) -> Optional[str]:
        """Đọc template từ file txt theo ngôn ngữ"""
        try:
            # Tạo tên file theo format: template_name_lang.txt
            template_file = self.templates_dir / f"{template_name}_{lang}.txt"

            if not template_file.exists():
                return None

            # Đọc nội dung file
            with open(template_file, 'r', encoding='utf-8') as f:
                return f.read().strip()

        except Exception:
            return None

    def replace_placeholders(self, template: str, card_info: Dict) -> str:
        """Thay thế placeholder trong template bằng thông tin từ card_info"""
        if not template or not card_info:
            return template

        # Thay thế các placeholder {field} bằng giá trị từ card_info
        for field, value in card_info.items():
            placeholder = f"{{{field}}}"
            if placeholder in template:
                # Nếu value rỗng, thay bằng chuỗi mặc định
                replacement = str(value).strip() if value else "không có thông tin"
                template = template.replace(placeholder, replacement)

        return template

    def text_to_speech_from_template(self, template_name: str, card_info: Dict,
                                   lang: Optional[str] = None, slow: bool = False) -> Optional[str]:
        """Tạo audio từ template và thông tin card"""
        # Auto-detect ngôn ngữ nếu không được chỉ định
        if lang is None:
            # Detect từ tên trong card_info
            name = card_info.get('name', '')
            lang = self.detect_language(name)

        # Đảm bảo ngôn ngữ được hỗ trợ
        if lang not in self.supported_languages:
            lang = 'en'  # Fallback to English

        # Đọc template theo ngôn ngữ
        template = self.load_template(template_name, lang)
        if not template:
            return None

        # Thay thế placeholder
        final_text = self.replace_placeholders(template, card_info)
        if not final_text:
            return None

        # Tạo audio từ text đã xử lý
        return self.text_to_speech(final_text, lang, slow)

    def cleanup_old_files(self, max_age_hours: int = 24):
        """Dọn dẹp các file audio cũ"""
        try:
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600

            for audio_file in self.audio_dir.glob("tts_*.mp3"):
                file_age = current_time - audio_file.stat().st_mtime
                if file_age > max_age_seconds:
                    try:
                        audio_file.unlink()
                    except Exception:
                        pass

        except Exception:
            pass

# Biến toàn cục cho TTS service
tts_service = None

def get_tts_service() -> TTSService:
    """Lấy instance TTS service toàn cục"""
    global tts_service
    if tts_service is None:
        tts_service = TTSService()
    return tts_service

def init_tts_service(audio_dir: str = "static/audio") -> TTSService:
    """Khởi tạo TTS service"""
    global tts_service
    tts_service = TTSService(audio_dir)
    return tts_service
