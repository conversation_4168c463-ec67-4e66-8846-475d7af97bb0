"""
Session Model - Quản lý phiên làm việc và dữ liệu đã chụp
Manage user sessions and captured data
"""

import os  # Thư viện hệ điều hành
import json  # Thư viện xử lý JSON
import uuid  # Thư viện tạo ID duy nhất
from datetime import datetime  # Thư viện ngày tháng
from pathlib import Path  # Thư viện xử lý đường dẫn

# Import file manager để lưu file có tổ chức - Import file manager for organized file saving
try:
    from utils.file_manager import file_manager
except ImportError:
    # Fallback khi chạy file riêng lẻ - Fallback when running file standalone
    file_manager = None
    print("⚠️ file_manager not available (running standalone)")


class SessionModel:
    """Model quản lý phiên làm việc người dùng và dữ liệu đã chụp - Model for managing user sessions and captured data"""

    def __init__(self):
        """Khởi tạo Session Model - chỉ quản lý dữ liệu trong bộ nhớ"""
        # Session chỉ tồn tại trong bộ nhớ - Sessions only exist in memory
        self.current_session = None  # Session hiện tại
        # print("📁 Session Model initialized")

    def create_session(self):
        """Tạo session mới (chỉ trong bộ nhớ) - Create a new session (in-memory only)"""
        session_id = str(uuid.uuid4())  # Tạo ID duy nhất cho session

        # Dữ liệu session mới - New session data
        session_data = {
            'session_id': session_id,  # ID phiên làm việc
            'created_at': datetime.now().isoformat(),  # Thời gian tạo
            'status': 'active',  # Trạng thái phiên
            'card_captured': False,  # Đã chụp name card chưa
            'face_captured': False,  # Đã chụp khuôn mặt chưa
            'card_image': None,  # Đường dẫn ảnh name card
            'face_image': None,  # Đường dẫn ảnh khuôn mặt
            'card_info': None,  # Thông tin đã trích xuất từ name card
            'generated_images': []  # Danh sách ảnh AI đã tạo
        }

        self.current_session = session_data  # Lưu session hiện tại

        # print(f"✅ New session created: {session_id}")
        return session_id  # Trả về ID session

    def save_session_data(self):
        """Lưu dữ liệu session (no-op - session chỉ trong bộ nhớ)"""
        # Session data chỉ tồn tại trong bộ nhớ - Session data only exists in memory
        return True

    def update_session(self, **kwargs):
        """Cập nhật dữ liệu session - Update session data"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        # Lặp qua các tham số được truyền vào
        for key, value in kwargs.items():
            if key in self.current_session:  # Nếu key tồn tại trong session
                self.current_session[key] = value  # Cập nhật giá trị

        self.current_session['updated_at'] = datetime.now().isoformat()  # Cập nhật thời gian sửa đổi
        return self.save_session_data()  # Lưu session data

    def save_captured_image(self, image_path, image_type):
        """Lưu tham chiếu ảnh đã chụp (không còn copy vào thư mục session) - Save captured image reference (no longer copying to session directory)"""
        if not self.current_session:  # Nếu không có session hiện tại
            return None  # Trả về None

        try:
            # Cập nhật dữ liệu session với đường dẫn ảnh gốc (không copy) - Update session data with original image path (no copying)
            if image_type == 'card':  # Nếu là ảnh name card
                self.current_session['card_captured'] = True  # Đánh dấu đã chụp card
                self.current_session['card_image'] = str(image_path)  # Lưu đường dẫn ảnh card
            elif image_type == 'face':  # Nếu là ảnh khuôn mặt
                self.current_session['face_captured'] = True  # Đánh dấu đã chụp face
                self.current_session['face_image'] = str(image_path)  # Lưu đường dẫn ảnh face

            self.save_session_data()  # Lưu session data
            print(f"✅ {image_type.title()} image reference saved: {image_path}")  # Log thành công
            return str(image_path)  # Trả về đường dẫn ảnh

        except Exception as e:
            print(f"❌ Error saving {image_type} image reference: {e}")  # Log lỗi
            return None  # Trả về None nếu có lỗi

    def save_card_info(self, card_info):
        """Lưu thông tin card đã trích xuất vào session và file có tổ chức - Save extracted card information to session and organized files"""
        print(f"DEBUG save_card_info called with: {card_info}")
        print(f"DEBUG card_info type: {type(card_info)}")
        print(f"DEBUG current_session exists: {self.current_session is not None}")

        if not self.current_session:  # Nếu không có session hiện tại
            print("ERROR No current session")
            return False  # Trả về False

        try:
            self.current_session['card_info'] = card_info  # Lưu thông tin card vào session
            print(f"DEBUG card_info saved to session: {self.current_session.get('card_info')}")

            # Lưu file có tổ chức sử dụng file manager (JSON + Text với tên người) - Save organized files using file manager (JSON + Text with person name)
            if file_manager:  # Nếu file_manager có sẵn
                file_results = file_manager.save_ocr_data(card_info)  # Gọi file manager để lưu
                if file_results:  # Nếu lưu thành công
                    self.current_session['ocr_files'] = file_results  # Lưu kết quả vào session
                    print(f"📁 OCR data organized for: {file_results.get('person_name', 'Unknown')}")  # Log tên người
            else:
                print("⚠️ file_manager not available, skipping file organization")

            self.save_session_data()  # Lưu session data
            print(f"✅ Card info saved to session")  # Log thành công
            return True  # Trả về True

        except Exception as e:
            print(f"❌ Error saving card info: {e}")  # Log lỗi
            return False  # Trả về False nếu có lỗi
    
    def add_generated_image(self, image_path, image_title="Generated Image"):
        """Thêm tham chiếu ảnh đã tạo (không còn copy vào thư mục session) - Add generated image reference (no longer copying to session directory)"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        try:
            # Ảnh AI đã được lưu trực tiếp bởi ai_generator.py sử dụng PathManager
            # AI images are already saved directly by ai_generator.py using PathManager
            image_index = len(self.current_session['generated_images']) + 1

            # Thêm vào danh sách ảnh đã tạo - Add to generated images list
            image_info = {
                'path': str(image_path),  # Đường dẫn ảnh (đã organized)
                'title': image_title,  # Tiêu đề ảnh
                'created_at': datetime.now().isoformat(),  # Thời gian tạo
                'filename': Path(image_path).name,  # Tên file
                'image_index': image_index  # Index ảnh
            }

            self.current_session['generated_images'].append(image_info)  # Thêm thông tin ảnh vào session
            self.save_session_data()  # Lưu session data

            print(f"✅ Generated image reference added: {image_path}")  # Log thêm tham chiếu ảnh
            return str(image_path)  # Trả về đường dẫn ảnh

        except Exception as e:
            print(f"❌ Error adding generated image: {e}")  # Log lỗi thêm ảnh
            return None  # Trả về None nếu có lỗi

    def save_generated_images(self, generated_images_list):
        """Lưu danh sách ảnh đã tạo vào session - Save list of generated images to session"""
        if not self.current_session:  # Nếu không có session hiện tại
            return False  # Trả về False

        try:
            # Xóa danh sách ảnh cũ và thêm danh sách mới - Clear old images and add new list
            self.current_session['generated_images'] = []

            for i, image_path in enumerate(generated_images_list, 1):
                # Thêm thông tin ảnh vào session - Add image info to session
                image_info = {
                    'path': str(image_path),  # Đường dẫn ảnh
                    'title': f'Generated Image {i}',  # Tiêu đề ảnh
                    'created_at': datetime.now().isoformat(),  # Thời gian tạo
                    'filename': Path(image_path).name,  # Tên file
                    'image_index': i  # Index ảnh
                }
                self.current_session['generated_images'].append(image_info)

            self.save_session_data()  # Lưu session data
            print(f"✅ Saved {len(generated_images_list)} generated images to session")  # Log thành công
            return True  # Trả về True

        except Exception as e:
            print(f"❌ Error saving generated images: {e}")  # Log lỗi
            return False  # Trả về False nếu có lỗi

    def get_session_status(self):
        """Lấy trạng thái session hiện tại - Get current session status"""
        if not self.current_session:  # Nếu không có session hiện tại
            return {
                'status': 'no_session',  # Trạng thái không có session
                'card_captured': False,  # Chưa chụp card
                'face_captured': False  # Chưa chụp face
            }

        # Trả về thông tin session hiện tại - Return current session info
        return {
            'status': 'active',  # Trạng thái hoạt động
            'session_id': self.current_session['session_id'],  # ID session
            'card_captured': self.current_session['card_captured'],  # Đã chụp card
            'face_captured': self.current_session['face_captured'],  # Đã chụp face
            'card_info': self.current_session.get('card_info'),  # Thông tin card
            'generated_images': self.current_session.get('generated_images', [])  # Danh sách ảnh đã tạo
        }

    def reset_session(self):
        """Reset session hiện tại - Reset current session"""
        if not self.current_session:  # Nếu không có session hiện tại
            return True  # Trả về True

        try:
            # Chỉ reset session trong bộ nhớ (không cần xóa file vì session chỉ trong memory)
            # Only reset session in memory (no need to delete files since session is memory-only)
            session_id = self.current_session['session_id']  # Lấy session ID để log
            self.current_session = None  # Đặt session hiện tại thành None
            print(f"✅ Session reset: {session_id}")  # Log reset session thành công
            return True  # Trả về True

        except Exception as e:
            print(f"❌ Error resetting session: {e}")  # Log lỗi reset session
            return False  # Trả về False nếu có lỗi

    def cleanup_old_sessions(self, days_old=7):
        """
        Dọn dẹp các session cũ hơn số ngày chỉ định - Clean up sessions older than specified days

        Vì session hiện tại chỉ tồn tại trong bộ nhớ, hàm này sẽ dọn dẹp các file output cũ
        Since current sessions only exist in memory, this function will clean up old output files
        """
        try:
            # Dọn dẹp file output cũ thay vì session files - Clean up old output files instead of session files
            from utils.helpers import cleanup_old_files

            # Dọn dẹp file trong thư mục outputs - Clean up files in outputs directory
            cleaned_count = cleanup_old_files('outputs', days_old=days_old)

            if cleaned_count > 0:  # Nếu có file được dọn dẹp
                print(f"🧹 Cleaned up {cleaned_count} old output files")  # Log số file đã dọn dẹp

            return cleaned_count  # Trả về số file đã dọn dẹp

        except Exception as e:
            print(f"❌ Error cleaning up old files: {e}")  # Log lỗi dọn dẹp file
            return 0  # Trả về 0 nếu có lỗi


# Test code khi chạy file trực tiếp - Test code when running file directly
if __name__ == "__main__":
    print("🧪 Testing SessionModel...")

    # Tạo session model test - Create test session model
    session_model = SessionModel()

    # Test tạo session - Test create session
    session_id = session_model.create_session()
    print(f"✅ Created session: {session_id}")

    # Test cập nhật session - Test update session
    session_model.update_session(card_captured=True)
    print("✅ Updated session")

    # Test lấy status - Test get status
    status = session_model.get_session_status()
    print(f"✅ Session status: {status}")

    # Test reset session - Test reset session
    session_model.reset_session()
    print("✅ Session reset")

    print("🎉 All tests passed!")
