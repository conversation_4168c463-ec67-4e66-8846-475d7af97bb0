#!/usr/bin/env python3
"""
File Manager Utility cho ứng dụng AI_Gen - Simplified version
Tiện ích quản lý file cho ứng dụng AI_Gen - Phiên bản đơn giản hóa
Chỉ xử lý core file operations, path handling được ủy thác cho PathManager
Only handles core file operations, path handling delegated to PathManager
"""

import json  # Thư viện xử lý JSON
from pathlib import Path  # Thư viện xử lý đường dẫn
from datetime import datetime  # Thư viện ngày tháng
from typing import Dict, List, Optional  # Thư viện type hints

# Import PathManager để xử lý đường dẫn tập trung
from utils.path_manager import path_manager


class FileManager:
    """Simplified file manager - chỉ xử lý core file operations"""

    def __init__(self):
        """Khởi tạo file manager đơn giản"""
        print("📁 File Manager initialized (simplified)")

    def save_ocr_data(self, card_info: Dict) -> Dict[str, str]:
        """
        <PERSON><PERSON>u dữ liệu OCR sử dụng PathManager để tạo đường dẫn
        Save OCR data using PathManager for path generation
        """
        try:
            # Sử dụng PathManager để tạo đường dẫn - Use PathManager for path generation
            person_name = card_info.get('name', 'Unknown_Person')
            file_paths = path_manager.get_ocr_file_paths(person_name)

            json_path = file_paths['json']
            text_path = file_paths['txt']

            # Lưu file JSON
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(card_info, f, ensure_ascii=False, indent=2)

            with open(text_path, 'w', encoding='utf-8') as f:  # Mở file text để ghi
                f.write("=== THÔNG TIN NAME CARD ===\n")  # Header tiếng Việt
                f.write(f"Trích xuất lúc: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")  # Thời gian trích xuất
                f.write("=" * 40 + "\n\n")  # Dòng phân cách

                f.write(f"👤 Tên: {card_info.get('name', 'N/A')}\n")  # Tên
                f.write(f"💼 Chức vụ: {card_info.get('title', card_info.get('occupation', 'N/A'))}\n")  # Chức vụ
                f.write(f"🏢 Công ty: {card_info.get('company', 'N/A')}\n")  # Công ty
                f.write(f"📧 Email: {card_info.get('email', card_info.get('gmail', 'N/A'))}\n")  # Email
                f.write(f"📱 Điện thoại: {card_info.get('phone', card_info.get('phone_number', 'N/A'))}\n")  # Điện thoại
                f.write(f"🌐 Website: {card_info.get('website', 'N/A')}\n")  # Website
                f.write(f"📍 Địa chỉ: {card_info.get('address', 'N/A')}\n")  # Địa chỉ

                # Thêm phần dữ liệu thô - Add raw data section
                f.write("\n" + "=" * 40 + "\n")
                f.write("DỮ LIỆU THÔI ĐÃ TRÍCH XUẤT:\n")  # Header dữ liệu thô
                f.write("=" * 40 + "\n")
                for key, value in card_info.items():  # Lặp qua tất cả dữ liệu
                    f.write(f"{key}: {value}\n")  # Ghi từng cặp key-value

            print(f"💾 OCR data saved for {person_name}")  # Log thành công
            print(f"   📄 JSON: {json_path}")  # Log đường dẫn JSON
            print(f"   📝 Text: {text_path}")  # Log đường dẫn text

            return {
                'json_path': str(json_path),  # Đường dẫn file JSON
                'text_path': str(text_path),  # Đường dẫn file text
                'person_name': person_name,  # Tên người
                'person_dir': str(json_path.parent)  # Đường dẫn thư mục người
            }

        except Exception as e:
            print(f"❌ Error saving OCR data: {e}")  # Log lỗi
            return {}  # Trả về dict rỗng nếu có lỗi
    
    # NOTE: save_ai_image method removed to eliminate double saving
    # AI images are now saved directly by ai_generator.py using PathManager

    def organize_person_files(self, card_info: Dict) -> Dict:
        """
        Tổ chức tất cả file cho một người (dữ liệu OCR + ảnh AI)
        Organize all files for a person (OCR data + AI images)

        Args:
            card_info: Dictionary chứa thông tin name card
                      Dictionary containing business card information

        Returns:
            Dictionary với thông tin file đã tổ chức
            Dictionary with organized file information
        """
        try:
            person_name = self.sanitize_filename(card_info.get('name', 'Unknown_Person'))  # Làm sạch tên người
            person_dir = self.outputs_dir / person_name  # Đường dẫn thư mục người

            # Lấy tất cả file cho người này - Get all files for this person
            ocr_files = list(person_dir.glob(f"{person_name}_card_info_*"))  # Danh sách file OCR
            ai_images = list(person_dir.glob(f"{person_name}_AI_image_*"))  # Danh sách ảnh AI

            # Sắp xếp theo thời gian tạo (mới nhất trước) - Sort by creation time (newest first)
            ocr_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)  # Sắp xếp file OCR
            ai_images.sort(key=lambda x: x.stat().st_mtime, reverse=True)  # Sắp xếp ảnh AI

            return {
                'person_name': person_name,  # Tên người
                'person_dir': str(person_dir),  # Đường dẫn thư mục người
                'ocr_files': [str(f) for f in ocr_files],  # Danh sách file OCR
                'ai_images': [str(f) for f in ai_images],  # Danh sách ảnh AI
                'total_files': len(ocr_files) + len(ai_images),  # Tổng số file
                'latest_ocr': str(ocr_files[0]) if ocr_files else None,  # File OCR mới nhất
                'latest_images': [str(f) for f in ai_images[:2]]  # 2 ảnh mới nhất - Latest 2 images
            }

        except Exception as e:
            print(f"❌ Error organizing person files: {e}")  # Log lỗi tổ chức file
            return {}  # Trả về dict rỗng nếu có lỗi
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """
        Clean up old files older than specified days
        
        Args:
            days_old: Number of days to keep files
            
        Returns:
            Number of files cleaned up
        """
        try:
            import time
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            cleaned_count = 0
            
            for person_dir in self.outputs_dir.iterdir():
                if person_dir.is_dir():
                    for file_path in person_dir.iterdir():
                        if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                            file_path.unlink()
                            cleaned_count += 1
                    
                    # Remove empty directories
                    if not any(person_dir.iterdir()):
                        person_dir.rmdir()
            
            print(f"🧹 Cleaned up {cleaned_count} old files")
            return cleaned_count
            
        except Exception as e:
            print(f"❌ Error during cleanup: {e}")
            return 0


# Global file manager instance
file_manager = FileManager()


def save_ocr_data(card_info: Dict) -> Dict[str, str]:
    """Convenience function to save OCR data"""
    return file_manager.save_ocr_data(card_info)


def save_ai_image(image_path: str, card_info: Dict, image_index: int = 1) -> Optional[str]:
    """Convenience function to save AI image"""
    return file_manager.save_ai_image(image_path, card_info, image_index)


def organize_person_files(card_info: Dict) -> Dict:
    """Convenience function to organize person files"""
    return file_manager.organize_person_files(card_info)
